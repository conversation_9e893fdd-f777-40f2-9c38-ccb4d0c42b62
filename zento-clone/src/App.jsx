import Header from './components/Header'
import Hero from './components/Hero'
import BlogGrid from './components/BlogGrid'
import Sidebar from './components/Sidebar'
import CTA from './components/CTA'
import Footer from './components/Footer'
import SVGSprite from './components/SVGSprite'

function App() {
  return (
    <div id="wrapper">
      <SVGSprite />
      <Header />
      <main id="home" className="main grid-container">
        <div className="content-wrapper">
          <Hero />
          <div className="sticky-container section np-bottom grid-container epcl-flex">
            <Sidebar />
            <div className="center left-content grid-70">
              <BlogGrid />
            </div>
          </div>
        </div>
      </main>

      <div className="clear"></div>
      <CTA />
      <Footer />
    </div>
  )
}

export default App
