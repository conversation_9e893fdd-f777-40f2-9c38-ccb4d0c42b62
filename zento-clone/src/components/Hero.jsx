import { useState } from 'react'

const Hero = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      setTimeout(() => setIsSubmitted(false), 3000)
      setEmail('')
    }
  }

  return (
    <div className="intro-text large-section np-bottom grid-container np-mobile epcl-flex">
      <div className="left grid-50 tablet-grid-55">
        <div className="text">
          <h1 className="title ularge fw-medium">
            Hey, I'm <span className="highlight"><PERSON></span> 👋
          </h1>
          <p>
            I'm a <strong>design technologist</strong> in Atlanta. I like working on the front-end of the web. 
            This is my site, <strong>Zento</strong> where I blog, share and write about my personal projects.
          </p>
        </div>
        <form className="subscribe-form" onSubmit={handleSubmit}>
          <label className="title small" htmlFor="subscribe-email">Let's connect</label>
          <div className="form-group">
            <input
              id="subscribe-email"
              type="email"
              name="email"
              className="inputbox large"
              required
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <button className="epcl-button submit absolute" type="submit">
              Get Started
              <span className="loader"></span>
            </button>
          </div>
          {isSubmitted && (
            <p className="success-message">
              Subscription was sent successfully, check your email <i className="fa fa-envelope-o"></i>
            </p>
          )}
        </form>
      </div>
      <div className="right grid-45 tablet-grid-45 mobile-grid-60">
        <img
          src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop&crop=face"
          alt="Jonathan Doe"
          className="hero-image fullwidth"
          width="442"
          height="442"
        />
      </div>
    </div>
  )
}

export default Hero
