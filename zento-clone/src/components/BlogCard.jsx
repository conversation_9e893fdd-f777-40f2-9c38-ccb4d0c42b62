const BlogCard = ({ post }) => {
  const renderStars = (rating) => {
    if (!rating) return null;
    
    return (
      <div className="difficulty meta-info">
        <svg className="icon main-color" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        <span className="name">Rating</span>
        <div className={`rating star-${rating}`}>
          {[1, 2, 3].map((star) => (
            <span key={star} className="star">★</span>
          ))}
        </div>
      </div>
    );
  };

  const renderAccessIcon = () => {
    if (post.access === 'members') {
      return (
        <a href="#" className="access-icon visibility-members meta-info tooltip hide-on-mobile" data-title="Members Article" aria-label="Members Article">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"/>
          </svg>
        </a>
      );
    }
    
    if (post.access === 'premium') {
      return (
        <>
          <a href="#" className="access-icon visibility-paid meta-info tooltip hide-on-mobile" data-title="Premium Article" aria-label="Premium Article">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </a>
          {post.featured && (
            <a href="#" className="access-icon visibility-members meta-info tooltip hide-on-mobile" data-title="Featured Article" aria-label="Featured Article">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                <path d="M11.9998 17L6.12197 20.5902L7.72007 13.8906L2.48926 9.40983L9.35479 8.85942L11.9998 2.5L14.6449 8.85942L21.5104 9.40983L16.2796 13.8906L17.8777 20.5902L11.9998 17Z"/>
              </svg>
            </a>
          )}
        </>
      );
    }
    
    return null;
  };

  return (
    <article className={`default classic-large bg-box ctag ctag-${post.category.toLowerCase()} post-access-${post.access}`}>
      <div className="post-format-image epcl-flex">
        <div className="featured-image">
          <a href="#" className="thumb epcl-loader opacity-effect">
            <img 
              className="fullimage cover" 
              loading="lazy" 
              src={post.image} 
              alt={`Image of: ${post.title}`}
            />
            <span className="screen-reader-text">{post.title}</span>
          </a>
          <div className="tags fill-color absolute">
            <a 
              href="#" 
              className={`primary-tag ctag ctag-${post.category.toLowerCase()}`}
              style={{ backgroundColor: post.categoryColor }}
            >
              {post.category}
            </a>
          </div>
        </div>

        <div className="info">
          <header>
            <div className="meta inline small">
              <span className="hide-on-desktop-sm hide-on-tablet hide-on-mobile">
                <time className="meta-info" dateTime={post.date}>
                  <svg className="icon main-color small" viewBox="0 0 24 24" width="16" height="16">
                    <path fill="currentColor" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                  </svg>
                  {post.date}
                </time>
              </span>
              
              <div className="min-read meta-info">
                <svg className="icon main-color" viewBox="0 0 24 24" width="16" height="16">
                  <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                {post.readTime}
              </div>
              
              {renderStars(post.rating)}
            </div>
            
            <h2 className="main-title title underline-effect">
              <a href="#">{post.title}</a>
            </h2>
            
            {renderAccessIcon()}
          </header>
          
          <div className="post-excerpt">
            <p>{post.excerpt}</p>
            <div className="clear"></div>
          </div>
          
          <footer className="bottom">
            <div className="meta bottom epcl-flex">
              <div className="tags">
                {post.tags.map((tag, index) => (
                  <a key={index} href="#" className={`ctag ctag-${tag.toLowerCase()}`}>
                    {tag}
                  </a>
                ))}
              </div>
              <a href="#" className="author">
                <img 
                  className="author-image cover" 
                  loading="lazy" 
                  src={post.author.image} 
                  alt={`Image of: ${post.author.name}`}
                />
                <span className="author-name">{post.author.name}</span>
              </a>
            </div>
            <div className="clear"></div>
          </footer>
        </div>
      </div>
      <div className="clear"></div>
    </article>
  );
};

export default BlogCard;
