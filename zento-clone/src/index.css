@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&family=Urbanist:wght@400;500;600;700;800&display=swap');

:root {
  --epcl-font-family: "DM Sans", sans-serif;
  --epcl-title-font-family: "Urbanist", sans-serif;
  --epcl-font-size: 16px;
  --epcl-line-height: 1.9;
  --epcl-font-size-editor: 17px;
  --epcl-line-height-editor: 1.9;
  --epcl-main-color: #FD71A7;
  --epcl-secondary-color: #FF2AAC;
  --epcl-titles-color: #282424;
  --epcl-black: #282424;
  --epcl-meta-color: #4D6385;
  --epcl-background-color: #FAF8FF;
  --epcl-boxes-background-color: #fff;
  --epcl-boxes-border-color: #E9E8FF;
  --epcl-border-color: #E9E8FF;
  --epcl-boxes-shadow: 0px 2px 5px 0px rgba(0,0,0,0.03);
  --epcl-small-shadow: 0px 3px 5px 0px rgba(0,0,0,0.2);
  --epcl-medium-shadow: 0px 8px 16px -4px rgba(0,0,0,0.10);
  --epcl-large-shadow: 0px 4px 25px -4px rgba(0,0,0,0.20);
  --epcl-transition-bezier: cubic-bezier(.5,2.5,.7,.7);
  --epcl-code-background: #211d3f;
  --epcl-code-color: #b0b9c5;
  --epcl-small-border-radius: 4px;
  --epcl-medium-border-radius: 10px;
  --epcl-large-border-radius: 100px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  box-sizing: border-box;
}

body {
  font-family: var(--epcl-font-family);
  font-size: var(--epcl-font-size);
  line-height: var(--epcl-line-height);
  color: var(--epcl-black);
  background-color: var(--epcl-background-color);
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
  outline: 0;
  color: inherit;
}

/* Clear Fix */
.clear {
  clear: both;
  display: block;
  overflow: hidden;
  visibility: hidden;
  width: 0;
  height: 0;
}

.clearfix::after,
.clearfix::before,
.grid-container::after,
.grid-container::before {
  content: ".";
  display: block;
  overflow: hidden;
  visibility: hidden;
  font-size: 0;
  line-height: 0;
  width: 0;
  height: 0;
}

.clearfix::after,
.grid-container::after {
  clear: both;
}

/* Grid System */
.grid-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1190px;
  padding-left: 20px;
  padding-right: 20px;
}

.grid-5, .grid-10, .grid-15, .grid-20, .grid-25, .grid-30, .grid-33, .grid-35, .grid-40, .grid-45, .grid-50, .grid-55, .grid-60, .grid-65, .grid-66, .grid-70, .grid-75, .grid-80, .grid-85, .grid-90, .grid-95, .grid-100,
.mobile-grid-5, .mobile-grid-10, .mobile-grid-15, .mobile-grid-20, .mobile-grid-25, .mobile-grid-30, .mobile-grid-33, .mobile-grid-35, .mobile-grid-40, .mobile-grid-45, .mobile-grid-50, .mobile-grid-55, .mobile-grid-60, .mobile-grid-65, .mobile-grid-66, .mobile-grid-70, .mobile-grid-75, .mobile-grid-80, .mobile-grid-85, .mobile-grid-90, .mobile-grid-95, .mobile-grid-100,
.tablet-grid-5, .tablet-grid-10, .tablet-grid-15, .tablet-grid-20, .tablet-grid-25, .tablet-grid-30, .tablet-grid-33, .tablet-grid-35, .tablet-grid-40, .tablet-grid-45, .tablet-grid-50, .tablet-grid-55, .tablet-grid-60, .tablet-grid-65, .tablet-grid-66, .tablet-grid-70, .tablet-grid-75, .tablet-grid-80, .tablet-grid-85, .tablet-grid-90, .tablet-grid-95, .tablet-grid-100 {
  box-sizing: border-box;
  padding-left: 20px;
  padding-right: 20px;
}

.grid-parent {
  padding-left: 0;
  padding-right: 0;
}

/* Mobile Grid */
@media screen and (max-width: 767px) {
  .hide-on-mobile { display: none !important; }
  .mobile-grid-5 { float: left; width: 5%; }
  .mobile-grid-10 { float: left; width: 10%; }
  .mobile-grid-15 { float: left; width: 15%; }
  .mobile-grid-20 { float: left; width: 20%; }
  .mobile-grid-25 { float: left; width: 25%; }
  .mobile-grid-30 { float: left; width: 30%; }
  .mobile-grid-35 { float: left; width: 35%; }
  .mobile-grid-40 { float: left; width: 40%; }
  .mobile-grid-45 { float: left; width: 45%; }
  .mobile-grid-50 { float: left; width: 50%; }
  .mobile-grid-55 { float: left; width: 55%; }
  .mobile-grid-60 { float: left; width: 60%; }
  .mobile-grid-65 { float: left; width: 65%; }
  .mobile-grid-70 { float: left; width: 70%; }
  .mobile-grid-75 { float: left; width: 75%; }
  .mobile-grid-80 { float: left; width: 80%; }
  .mobile-grid-85 { float: left; width: 85%; }
  .mobile-grid-90 { float: left; width: 90%; }
  .mobile-grid-95 { float: left; width: 95%; }
  .mobile-grid-33 { float: left; width: 33.3333%; }
  .mobile-grid-66 { float: left; width: 66.6667%; }
  .mobile-grid-100 { clear: both; width: 100%; }
}

/* Tablet Grid */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .hide-on-tablet { display: none !important; }
  .tablet-grid-5 { float: left; width: 5%; }
  .tablet-grid-10 { float: left; width: 10%; }
  .tablet-grid-15 { float: left; width: 15%; }
  .tablet-grid-20 { float: left; width: 20%; }
  .tablet-grid-25 { float: left; width: 25%; }
  .tablet-grid-30 { float: left; width: 30%; }
  .tablet-grid-35 { float: left; width: 35%; }
  .tablet-grid-40 { float: left; width: 40%; }
  .tablet-grid-45 { float: left; width: 45%; }
  .tablet-grid-50 { float: left; width: 50%; }
  .tablet-grid-55 { float: left; width: 55%; }
  .tablet-grid-60 { float: left; width: 60%; }
  .tablet-grid-65 { float: left; width: 65%; }
  .tablet-grid-70 { float: left; width: 70%; }
  .tablet-grid-75 { float: left; width: 75%; }
  .tablet-grid-80 { float: left; width: 80%; }
  .tablet-grid-85 { float: left; width: 85%; }
  .tablet-grid-90 { float: left; width: 90%; }
  .tablet-grid-95 { float: left; width: 95%; }
  .tablet-grid-33 { float: left; width: 33.3333%; }
  .tablet-grid-66 { float: left; width: 66.6667%; }
  .tablet-grid-100 { clear: both; width: 100%; }
}

/* Desktop Grid */
@media screen and (min-width: 1024px) {
  .hide-on-desktop { display: none !important; }
  .grid-5 { float: left; width: 5%; }
  .grid-10 { float: left; width: 10%; }
  .grid-15 { float: left; width: 15%; }
  .grid-20 { float: left; width: 20%; }
  .grid-25 { float: left; width: 25%; }
  .grid-30 { float: left; width: 30%; }
  .grid-35 { float: left; width: 35%; }
  .grid-40 { float: left; width: 40%; }
  .grid-45 { float: left; width: 45%; }
  .grid-50 { float: left; width: 50%; }
  .grid-55 { float: left; width: 55%; }
  .grid-60 { float: left; width: 60%; }
  .grid-65 { float: left; width: 65%; }
  .grid-70 { float: left; width: 70%; }
  .grid-75 { float: left; width: 75%; }
  .grid-80 { float: left; width: 80%; }
  .grid-85 { float: left; width: 85%; }
  .grid-90 { float: left; width: 90%; }
  .grid-95 { float: left; width: 95%; }
  .grid-33 { float: left; width: 33.3333%; }
  .grid-66 { float: left; width: 66.6667%; }
  .grid-100 { clear: both; width: 100%; }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
  font-family: var(--epcl-title-font-family);
  color: var(--epcl-titles-color);
}

h1 { font-size: 26px; }
h2 { font-size: 24px; }
h3 { font-size: 22px; }
h4 { font-size: 20px; }
h5 { font-size: 18px; }
h6 { font-size: 16px; }

.title {
  font-family: var(--epcl-title-font-family);
  font-weight: 800;
  font-size: 30px;
  margin-bottom: 40px;
  line-height: 1.45;
  color: var(--epcl-titles-color);
  letter-spacing: 0;
}

.title a {
  color: var(--epcl-titles-color);
}

.title.ularge {
  font-size: 42px;
  margin-bottom: 70px;
  line-height: 1.2;
  font-weight: 800;
  letter-spacing: 0.25px;
}

.title.large {
  font-size: 34px;
  line-height: 1.3;
}

.title.medium {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 30px;
}

.title.small {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
}

.title.usmall {
  font-size: 15px;
  margin-bottom: 5px;
  letter-spacing: 0.4px;
  font-weight: 700;
}

.title.no-margin {
  margin-bottom: 0;
}

.highlight {
  color: var(--epcl-main-color);
}

/* Flexbox utilities */
.epcl-flex {
  display: flex;
  align-items: center;
}

/* Buttons */
.epcl-button {
  display: inline-block;
  color: white;
  font-size: 16px;
  padding: 5px 22px;
  position: relative;
  z-index: 2;
  font-family: var(--epcl-title-font-family);
  font-weight: 500;
  border-radius: var(--epcl-small-border-radius);
  background-color: var(--epcl-main-color);
  letter-spacing: 0.1px;
  transition: 300ms;
  border: none;
  cursor: pointer;
}

.epcl-button:hover {
  color: white;
  background: var(--epcl-black);
}

.epcl-button.black {
  background: var(--epcl-black);
}

.epcl-button.black:hover {
  background: #000;
}

.epcl-button.large {
  font-size: 20px;
  padding: 8px 40px;
}

.epcl-button.submit {
  position: absolute;
  right: 5px;
  top: 5px;
  bottom: 5px;
  line-height: 1;
}

.epcl-button.absolute {
  position: absolute;
  right: 5px;
  top: 5px;
  bottom: 5px;
  line-height: 1;
}

/* Form Elements */
.inputbox {
  display: block;
  background: white;
  color: var(--epcl-black);
  font-size: 14px;
  font-family: var(--epcl-title-font-family);
  line-height: 1.8;
  letter-spacing: 0.25px;
  padding: 15px 25px;
  height: 45px;
  width: 100%;
  margin-bottom: 15px;
  box-sizing: border-box;
  outline: 0;
  border: 1px solid var(--epcl-border-color);
  box-shadow: var(--epcl-boxes-shadow);
  border-radius: var(--epcl-small-border-radius);
  transition: 300ms;
}

.inputbox.large {
  height: 54px;
  padding: 15px 30px;
  font-size: 15px;
  letter-spacing: 0.5px;
}

.inputbox:focus {
  border-color: #ddd;
}

.inputbox::placeholder {
  color: #667595;
}

.form-group {
  position: relative;
  z-index: 2;
}

/* Layout */
.section {
  padding: 80px 0;
}

.large-section {
  padding: 120px 0;
}

.np-bottom {
  padding-bottom: 0;
}

.np-mobile {
  padding: 0;
}

@media screen and (max-width: 767px) {
  .np-mobile {
    padding: 0;
  }
}

/* Main Layout */
#wrapper {
  width: 100%;
}

.main {
  width: 100%;
}

.content-wrapper {
  width: 100%;
}

/* Hero Section */
.intro-text {
  display: flex;
  align-items: center;
  gap: 40px;
}

.intro-text .left {
  flex: 1;
}

.intro-text .right {
  flex: 1;
}

.intro-text .text {
  margin-bottom: 40px;
}

.hero-image {
  width: 100%;
  height: auto;
  border-radius: var(--epcl-medium-border-radius);
}

.fullwidth {
  width: 100%;
}

/* Subscribe Form */
.subscribe-form {
  margin-top: 30px;
}

.subscribe-form .title {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.success-message {
  color: var(--epcl-main-color);
  font-weight: 600;
  margin-top: 10px;
  display: none;
}

.error-detail {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 10px;
}

/* Articles */
.articles {
  margin-top: 60px;
}

.articles.classic {
  display: grid;
  gap: 40px;
}

article {
  background: var(--epcl-boxes-background-color);
  border-radius: var(--epcl-medium-border-radius);
  box-shadow: var(--epcl-boxes-shadow);
  border: 1px solid var(--epcl-boxes-border-color);
  overflow: hidden;
  transition: 300ms;
}

article:hover {
  transform: translateY(-2px);
  box-shadow: var(--epcl-small-shadow);
}

.post-format-image {
  display: flex;
  gap: 30px;
  padding: 30px;
}

.featured-image {
  position: relative;
  flex-shrink: 0;
  width: 200px;
  height: 200px;
  border-radius: var(--epcl-small-border-radius);
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 300ms;
}

.featured-image:hover img {
  transform: scale(1.05);
}

.thumb {
  display: block;
  width: 100%;
  height: 100%;
}

.fullimage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover {
  object-fit: cover;
}

/* Tags */
.tags {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
}

.tags.fill-color a {
  display: inline-block;
  background: var(--epcl-main-color);
  color: white;
  padding: 5px 15px;
  border-radius: var(--epcl-large-border-radius);
  font-weight: 600;
  font-size: 13px;
  font-family: var(--epcl-title-font-family);
  letter-spacing: 0.25px;
}

.tags.fill-color a.primary-tag {
  margin-left: -25px;
  border-radius: 0 var(--epcl-large-border-radius) var(--epcl-large-border-radius) 0;
  padding-left: 25px;
  padding-right: 25px;
}

.tags.absolute {
  position: absolute;
  left: 0;
  top: 25px;
  z-index: 10;
}

/* Article Info */
.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-excerpt p {
  color: var(--epcl-meta-color);
  line-height: 1.7;
  margin-bottom: 20px;
}

/* Effects */
.opacity-effect:hover {
  opacity: 0.87;
}

.epcl-loader {
  position: relative;
  overflow: hidden;
}

.screen-reader-text {
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* CTA Section */
.epcl-cta {
  background: var(--epcl-boxes-background-color);
  border: 1px solid var(--epcl-boxes-border-color);
  padding: 60px 0;
  margin: 80px 0;
  position: relative;
  overflow: hidden;
}

.epcl-cta.no-border-radius {
  border-radius: 0;
}

.epcl-cta .grid-container {
  position: relative;
  z-index: 2;
}

.epcl-cta .left {
  padding-right: 40px;
}

.epcl-cta .right {
  text-align: center;
}

.epcl-cta .bg,
.epcl-cta .bg2 {
  position: absolute;
  opacity: 0.1;
  pointer-events: none;
}

.epcl-cta .bg {
  top: -50px;
  left: -50px;
}

.epcl-cta .bg2 {
  bottom: -50px;
  right: -50px;
}

.epcl-cta .main-color {
  color: var(--epcl-main-color);
}

.epcl-cta .secondary-color {
  color: var(--epcl-secondary-color);
}

.textcenter {
  text-align: center;
}

/* Background Box */
.bg-box {
  background: var(--epcl-boxes-background-color);
  border: 1px solid var(--epcl-boxes-border-color);
  border-radius: var(--epcl-medium-border-radius);
  box-shadow: var(--epcl-boxes-shadow);
}

/* Footer */
#footer {
  background: var(--epcl-boxes-background-color);
  border-top: 1px solid var(--epcl-border-color);
  padding: 60px 0 40px;
  margin-top: 80px;
}

.widgets {
  margin-bottom: 40px;
}

.widget {
  margin-bottom: 40px;
}

.widget-title {
  font-family: var(--epcl-title-font-family);
  font-weight: 800;
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--epcl-titles-color);
}

.widget_epcl_social .icons {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widget_epcl_social .icons li {
  margin-bottom: 15px;
}

.widget_epcl_social .icons a {
  display: flex;
  align-items: center;
  padding: 12px 0;
  transition: 300ms;
}

.widget_epcl_social .icons a:hover {
  transform: translateX(5px);
}

.widget_epcl_social .icons .name {
  flex: 1;
  font-size: 14px;
}

.widget_epcl_social .icons .icon {
  width: 24px;
  height: 24px;
  margin-left: 10px;
}

.widget_epcl_social .icons svg {
  width: 100%;
  height: 100%;
  fill: var(--epcl-main-color);
}

.widget_menu .menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widget_menu .menu li {
  border-top: 1px solid var(--epcl-border-color);
  padding: 10px 0;
}

.widget_menu .menu li:first-child {
  border-top: none;
  padding-top: 0;
}

.widget_menu .menu a {
  font-size: 14px;
  color: var(--epcl-black);
  transition: 300ms;
}

.widget_menu .menu a:hover {
  color: var(--epcl-main-color);
}

.logo img {
  max-width: 170px;
  height: auto;
}

.published {
  text-align: center;
  font-size: 14px;
  color: var(--epcl-meta-color);
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--epcl-border-color);
}

.published a {
  color: var(--epcl-black);
  font-weight: 600;
}

.published .dot {
  margin: 0 10px;
}

.published .dot::before {
  content: "•";
}

/* Responsive */
@media screen and (max-width: 1023px) {
  .intro-text {
    flex-direction: column;
    text-align: center;
  }

  .intro-text .left,
  .intro-text .right {
    width: 100%;
  }

  .post-format-image {
    flex-direction: column;
  }

  .featured-image {
    width: 100%;
    height: 250px;
  }

  .epcl-cta .left {
    padding-right: 0;
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 767px) {
  .section {
    padding: 60px 0;
  }

  .large-section {
    padding: 80px 0;
  }

  .title.ularge {
    font-size: 32px;
    margin-bottom: 40px;
  }

  .post-format-image {
    padding: 20px;
  }

  .featured-image {
    height: 200px;
  }
}

/* Additional Blog Card Styles */
.meta.inline {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  font-size: 13px;
  color: var(--epcl-meta-color);
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.meta-info svg {
  width: 16px;
  height: 16px;
  fill: var(--epcl-main-color);
}

.main-title {
  margin-bottom: 15px;
}

.main-title a {
  color: var(--epcl-titles-color);
  transition: 300ms;
}

.main-title a:hover {
  color: var(--epcl-main-color);
}

.underline-effect a:hover {
  text-decoration: underline;
}

.meta.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.meta.bottom .tags {
  display: flex;
  gap: 10px;
}

.meta.bottom .tags a {
  font-size: 12px;
  color: var(--epcl-meta-color);
  padding: 4px 8px;
  border-radius: var(--epcl-small-border-radius);
  background: var(--epcl-background-color);
  transition: 300ms;
}

.meta.bottom .tags a:hover {
  color: var(--epcl-main-color);
  background: var(--epcl-boxes-border-color);
}

.author {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: var(--epcl-black);
  transition: 300ms;
}

.author:hover {
  color: var(--epcl-main-color);
}

.author-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-weight: 600;
}

/* Rating Stars */
.rating {
  display: flex;
  gap: 2px;
}

.rating .star {
  color: #ddd;
  font-size: 14px;
}

.rating.star-1 .star:nth-child(1) {
  color: var(--epcl-main-color);
}

.rating.star-2 .star:nth-child(1),
.rating.star-2 .star:nth-child(2) {
  color: var(--epcl-main-color);
}

.rating.star-3 .star:nth-child(1),
.rating.star-3 .star:nth-child(2),
.rating.star-3 .star:nth-child(3) {
  color: var(--epcl-main-color);
}

/* Access Icons */
.access-icon {
  margin-left: 10px;
  color: var(--epcl-main-color);
}

.access-icon svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* Success message animation */
.success-message {
  display: block !important;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Hover effects */
.ctag:hover {
  color: white !important;
  background: var(--epcl-black) !important;
}

/* Fix for flexbox layout */
.epcl-flex {
  display: flex;
  align-items: flex-start;
  gap: 30px;
}

/* Ensure proper spacing */
.np-mobile {
  padding: 0;
}

@media screen and (max-width: 767px) {
  .np-mobile {
    padding: 0 !important;
  }

  .epcl-flex {
    flex-direction: column;
    gap: 20px;
  }

  .meta.bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
