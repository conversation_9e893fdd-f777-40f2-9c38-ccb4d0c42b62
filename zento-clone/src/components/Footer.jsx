import { useState } from 'react'

const Footer = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      setTimeout(() => setIsSubmitted(false), 3000)
      setEmail('')
    }
  }

  const socialLinks = [
    {
      name: "WhatsApp",
      url: "https://wa.me/5492996155777",
      icon: (
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
        </svg>
      )
    },
    {
      name: "Instagram",
      url: "https://www.instagram.com/estudiopatagon/",
      icon: (
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      )
    },
    {
      name: "Facebook",
      url: "https://www.facebook.com/ghost",
      icon: (
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    },
    {
      name: "Twitter",
      url: "https://twitter.com/tryghost",
      icon: (
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path fill="currentColor" d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
        </svg>
      )
    }
  ]

  const quickLinks = [
    { name: "Membership", url: "#" },
    { name: "Author Page", url: "#" },
    { name: "Sign In", url: "#" },
    { name: "Subscribe / Sign Up", url: "#" },
    { name: "404 Page", url: "#" }
  ]

  return (
    <footer id="footer">
      <div className="widgets grid-container">
        <div className="desktop-footer hide-on-mobile hide-on-tablet">
          {/* Social Widget */}
          <div className="widget widget_epcl_social widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h3 className="widget-title title medium bordered">
              <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Follow Us</span>
            </h3>
            <ul className="icons">
              {socialLinks.map((social, index) => (
                <li key={index}>
                  <a href={social.url} className={social.name.toLowerCase()} target="_blank" rel="noopener noreferrer">
                    <span className="name">Follow on <b>{social.name}</b></span>
                    <span className="icon">{social.icon}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links Widget */}
          <section className="widget widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h2 className="widget-title title medium bordered">
              <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Quick Links</span>
            </h2>
            <nav className="secondary-nav grid-container grid-small textcenter">
              <ul className="menu">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <span className="sep"></span>
                    <a href={link.url}>{link.name}</a>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="clear"></div>
          </section>

          {/* Logo and Subscribe Widget */}
          <section className="widget widget_text grid-33 tablet-grid-50 mobile-grid-100">
            <div className="logo">
              <a href="#">
                <img 
                  src="https://via.placeholder.com/170x60/FD71A7/FFFFFF?text=Zento" 
                  alt="Zento" 
                  width="170" 
                  height="60" 
                />
              </a>
            </div>
            <div className="textwidget">
              <p>Subscribe to our email newsletter and unlock access to <b>members-only</b> content and <b>exclusive updates.</b></p>
            </div>
            <br />
            <form className="subscribe-form" onSubmit={handleSubmit}>
              <label className="title small">Let's connect</label>
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  className="inputbox large"
                  required
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <button className="epcl-button submit absolute" type="submit">
                  Get Started
                  <span className="loader"></span>
                </button>
              </div>
              {isSubmitted && (
                <p className="success-message">
                  Subscription was sent successfully, check your email <i className="fa fa-envelope-o"></i>
                </p>
              )}
            </form>
            <div className="clear"></div>
          </section>

          <div className="clear"></div>
        </div>

        {/* Mobile Footer */}
        <div className="mobile-footer hide-on-desktop">
          <section className="widget widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h2 className="widget-title title medium bordered">
              <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Quick Links</span>
            </h2>
            <nav className="secondary-nav grid-container grid-small textcenter">
              <ul className="menu">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <span className="sep"></span>
                    <a href={link.url}>{link.name}</a>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="clear"></div>
          </section>

          <section className="widget widget_text grid-33 tablet-grid-50 mobile-grid-100">
            <div className="logo">
              <a href="#">
                <img 
                  src="https://via.placeholder.com/170x60/FD71A7/FFFFFF?text=Zento" 
                  alt="Zento" 
                  width="170" 
                  height="60" 
                />
              </a>
            </div>
            <div className="textwidget">
              <p>Subscribe to our email newsletter and unlock access to <b>members-only</b> content and <b>exclusive updates.</b></p>
            </div>
            <br />
            <form className="subscribe-form" onSubmit={handleSubmit}>
              <label className="title small">Let's connect</label>
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  className="inputbox large"
                  required
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <button className="epcl-button submit absolute" type="submit">
                  Get Started
                  <span className="loader"></span>
                </button>
              </div>
              {isSubmitted && (
                <p className="success-message">
                  Subscription was sent successfully, check your email <i className="fa fa-envelope-o"></i>
                </p>
              )}
            </form>
            <div className="clear"></div>
          </section>
        </div>
      </div>

      <p className="published underline-effect">
        <a href="https://estudiopatagon.com/projects/zento-for-ghost/" target="_blank" rel="noopener noreferrer">Zento</a> Theme by{' '}
        <a href="https://estudiopatagon.com/" target="_blank" rel="noopener noreferrer">EstudioPatagon</a>
        <span className="dot"></span> Powered by{' '}
        <a href="https://ghost.org/" target="_blank" rel="noopener noreferrer">Ghost</a>
      </p>
      <div className="clear"></div>
    </footer>
  )
}

export default Footer
