{"version": 3, "names": ["_index", "require", "getQualifiedName", "node", "isIdentifier", "name", "isThisExpression", "right", "left", "removeTypeDuplicates", "nodesIn", "nodes", "Array", "from", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "includes", "isTSAnyKeyword", "isTSBaseType", "set", "type", "isTSUnionType", "has", "push", "add", "typeArgumentsKey", "isTSTypeReference", "typeArguments", "typeName", "existing", "get", "existingTypeArguments", "params", "baseType", "genericName"], "sources": ["../../../src/modifications/typescript/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isThisExpression,\n  isTSAnyKeyword,\n  isTSTypeReference,\n  isTSUnionType,\n  isTSBaseType,\n} from \"../../validators/generated/index.ts\";\nimport type * as t from \"../../index.ts\";\n\nfunction getQualifiedName(node: t.TSTypeReference[\"typeName\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : isThisExpression(node)\n      ? \"this\"\n      : `${node.right.name}.${getQualifiedName(node.left)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  nodesIn: ReadonlyArray<t.TSType>,\n): Array<t.TSType> {\n  const nodes = Array.from(nodesIn);\n\n  const generics = new Map<string, t.TSTypeReference>();\n  const bases = new Map<t.TSBaseType[\"type\"], t.TSBaseType>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.TSType[]>();\n\n  const types: t.TSType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.includes(node)) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isTSAnyKeyword(node)) {\n      return [node];\n    }\n\n    // Analogue of FlowBaseAnnotation\n    if (isTSBaseType(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isTSUnionType(node)) {\n      if (!typeGroups.has(node.types)) {\n        nodes.push(...node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // todo: support merging tuples: number[]\n    const typeArgumentsKey = process.env.BABEL_8_BREAKING\n      ? \"typeArguments\"\n      : \"typeParameters\";\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n    if (isTSTypeReference(node) && node[typeArgumentsKey]) {\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      const typeArguments = node[typeArgumentsKey];\n      const name = getQualifiedName(node.typeName);\n\n      if (generics.has(name)) {\n        let existing: t.TypeScript = generics.get(name);\n        // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n        const existingTypeArguments = existing[typeArgumentsKey];\n        if (existingTypeArguments) {\n          existingTypeArguments.params.push(...typeArguments.params);\n          existingTypeArguments.params = removeTypeDuplicates(\n            existingTypeArguments.params,\n          );\n        } else {\n          existing = typeArguments;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAUA,SAASC,gBAAgBA,CAACC,IAAmC,EAAU;EACrE,OAAO,IAAAC,mBAAY,EAACD,IAAI,CAAC,GACrBA,IAAI,CAACE,IAAI,GACT,IAAAC,uBAAgB,EAACH,IAAI,CAAC,GACpB,MAAM,GACN,GAAGA,IAAI,CAACI,KAAK,CAACF,IAAI,IAAIH,gBAAgB,CAACC,IAAI,CAACK,IAAI,CAAC,EAAE;AAC3D;AAKe,SAASC,oBAAoBA,CAC1CC,OAAgC,EACf;EACjB,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC;EAEjC,MAAMI,QAAQ,GAAG,IAAIC,GAAG,CAA4B,CAAC;EACrD,MAAMC,KAAK,GAAG,IAAID,GAAG,CAAqC,CAAC;EAG3D,MAAME,UAAU,GAAG,IAAIC,GAAG,CAAa,CAAC;EAExC,MAAMC,KAAiB,GAAG,EAAE;EAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMjB,IAAI,GAAGQ,KAAK,CAACS,CAAC,CAAC;IACrB,IAAI,CAACjB,IAAI,EAAE;IAGX,IAAIgB,KAAK,CAACG,QAAQ,CAACnB,IAAI,CAAC,EAAE;MACxB;IACF;IAGA,IAAI,IAAAoB,qBAAc,EAACpB,IAAI,CAAC,EAAE;MACxB,OAAO,CAACA,IAAI,CAAC;IACf;IAGA,IAAI,IAAAqB,mBAAY,EAACrB,IAAI,CAAC,EAAE;MACtBa,KAAK,CAACS,GAAG,CAACtB,IAAI,CAACuB,IAAI,EAAEvB,IAAI,CAAC;MAC1B;IACF;IAEA,IAAI,IAAAwB,oBAAa,EAACxB,IAAI,CAAC,EAAE;MACvB,IAAI,CAACc,UAAU,CAACW,GAAG,CAACzB,IAAI,CAACgB,KAAK,CAAC,EAAE;QAC/BR,KAAK,CAACkB,IAAI,CAAC,GAAG1B,IAAI,CAACgB,KAAK,CAAC;QACzBF,UAAU,CAACa,GAAG,CAAC3B,IAAI,CAACgB,KAAK,CAAC;MAC5B;MACA;IACF;IAGA,MAAMY,gBAAgB,GAElB,gBAAgB;IAEpB,IAAI,IAAAC,wBAAiB,EAAC7B,IAAI,CAAC,IAAIA,IAAI,CAAC4B,gBAAgB,CAAC,EAAE;MAErD,MAAME,aAAa,GAAG9B,IAAI,CAAC4B,gBAAgB,CAAC;MAC5C,MAAM1B,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAAC+B,QAAQ,CAAC;MAE5C,IAAIpB,QAAQ,CAACc,GAAG,CAACvB,IAAI,CAAC,EAAE;QACtB,IAAI8B,QAAsB,GAAGrB,QAAQ,CAACsB,GAAG,CAAC/B,IAAI,CAAC;QAE/C,MAAMgC,qBAAqB,GAAGF,QAAQ,CAACJ,gBAAgB,CAAC;QACxD,IAAIM,qBAAqB,EAAE;UACzBA,qBAAqB,CAACC,MAAM,CAACT,IAAI,CAAC,GAAGI,aAAa,CAACK,MAAM,CAAC;UAC1DD,qBAAqB,CAACC,MAAM,GAAG7B,oBAAoB,CACjD4B,qBAAqB,CAACC,MACxB,CAAC;QACH,CAAC,MAAM;UACLH,QAAQ,GAAGF,aAAa;QAC1B;MACF,CAAC,MAAM;QACLnB,QAAQ,CAACW,GAAG,CAACpB,IAAI,EAAEF,IAAI,CAAC;MAC1B;MAEA;IACF;IAEAgB,KAAK,CAACU,IAAI,CAAC1B,IAAI,CAAC;EAClB;EAGA,KAAK,MAAM,GAAGoC,QAAQ,CAAC,IAAIvB,KAAK,EAAE;IAChCG,KAAK,CAACU,IAAI,CAACU,QAAQ,CAAC;EACtB;EAGA,KAAK,MAAM,GAAGC,WAAW,CAAC,IAAI1B,QAAQ,EAAE;IACtCK,KAAK,CAACU,IAAI,CAACW,WAAW,CAAC;EACzB;EAEA,OAAOrB,KAAK;AACd", "ignoreList": []}